/*
 * Supabase Password Reset Script
 * 
 * This script resets a user's password using the Supabase Admin API.
 * 
 * SETUP INSTRUCTIONS:
 * 
 * 1. Install Node.js if you haven't already:
 *    - Download from https://nodejs.org/
 *    - Or use a package manager like Homebrew: `brew install node`
 * 
 * 2. Initialize a new Node.js project:
 *    npm init -y
 * 
 * 3. Install Axios:
 *    npm install axios
 * 
 * 4. Save this script as 'reset-password.js'
 * 
 * 5. Replace the placeholder values below with your actual:
 *    - Supabase project URL
 *    - Service role key (keep this secret!)
 *    - User email
 *    - New password
 * 
 * 6. Run the script:
 *    node reset-password.js
 * 
 * SECURITY WARNING:
 * - The service role key has admin privileges and should NEVER be exposed in frontend applications
 * - Keep this key secret and only use it in secure server environments
 * - Consider using environment variables for sensitive data in production
 */

const axios = require('axios');

// ============================================================================
// CONFIGURATION - REPLACE THESE PLACEHOLDERS WITH YOUR ACTUAL VALUES
// ============================================================================

// Your Supabase project URL (format: https://your-project-ref.supabase.co)
const SUPABASE_URL = 'https://your-project-ref.supabase.co';

// Your Supabase service role key (found in Project Settings > API)
// WARNING: Keep this secret! Never expose in frontend code!
const SERVICE_ROLE_KEY = 'your-service-role-key-here';

// User email to reset password for
const USER_EMAIL = '<EMAIL>';

// New password to set
const NEW_PASSWORD = 'new-secure-password';

// ============================================================================
// SCRIPT LOGIC - DO NOT MODIFY BELOW THIS LINE
// ============================================================================

// Create axios instance with default headers
const supabaseAdmin = axios.create({
  baseURL: `${SUPABASE_URL}/auth/v1/admin`,
  headers: {
    'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
    'Content-Type': 'application/json',
    'apikey': SERVICE_ROLE_KEY
  }
});

/**
 * Fetches user ID by email from Supabase Admin API
 * @param {string} email - User email address
 * @returns {Promise<string>} User ID
 */
async function getUserIdByEmail(email) {
  try {
    console.log(`🔍 Looking up user with email: ${email}`);
    
    const response = await supabaseAdmin.get(`/users/by-email`, {
      params: { email }
    });
    
    if (response.data && response.data.id) {
      console.log(`✅ Found user with ID: ${response.data.id}`);
      return response.data.id;
    } else {
      throw new Error('User not found or invalid response format');
    }
  } catch (error) {
    if (error.response?.status === 404) {
      throw new Error(`User with email '${email}' not found`);
    } else if (error.response?.status === 401) {
      throw new Error('Unauthorized - check your service role key');
    } else if (error.response?.status === 403) {
      throw new Error('Forbidden - service role key may not have admin privileges');
    } else {
      throw new Error(`Failed to fetch user: ${error.message}`);
    }
  }
}

/**
 * Updates user password using Supabase Admin API
 * @param {string} userId - User ID
 * @param {string} newPassword - New password
 */
async function updateUserPassword(userId, newPassword) {
  try {
    console.log(`🔄 Updating password for user ID: ${userId}`);
    
    const response = await supabaseAdmin.put(`/users/${userId}`, {
      password: newPassword
    });
    
    if (response.status === 200) {
      console.log('✅ Password updated successfully!');
      console.log('📧 User will receive an email notification about the password change');
    } else {
      throw new Error(`Unexpected response status: ${response.status}`);
    }
  } catch (error) {
    if (error.response?.status === 400) {
      throw new Error('Bad request - check password requirements');
    } else if (error.response?.status === 401) {
      throw new Error('Unauthorized - check your service role key');
    } else if (error.response?.status === 403) {
      throw new Error('Forbidden - service role key may not have admin privileges');
    } else if (error.response?.status === 404) {
      throw new Error(`User with ID '${userId}' not found`);
    } else {
      throw new Error(`Failed to update password: ${error.message}`);
    }
  }
}

/**
 * Main function to reset user password
 */
async function resetUserPassword() {
  try {
    // Validate configuration
    if (SUPABASE_URL === 'https://your-project-ref.supabase.co') {
      throw new Error('Please replace SUPABASE_URL with your actual Supabase project URL');
    }
    
    if (SERVICE_ROLE_KEY === 'your-service-role-key-here') {
      throw new Error('Please replace SERVICE_ROLE_KEY with your actual service role key');
    }
    
    if (USER_EMAIL === '<EMAIL>') {
      throw new Error('Please replace USER_EMAIL with the actual user email');
    }
    
    if (NEW_PASSWORD === 'new-secure-password') {
      throw new Error('Please replace NEW_PASSWORD with the actual new password');
    }
    
    console.log('🚀 Starting password reset process...');
    console.log(`📧 Target email: ${USER_EMAIL}`);
    
    // Step 1: Get user ID by email
    const userId = await getUserIdByEmail(USER_EMAIL);
    
    // Step 2: Update user password
    await updateUserPassword(userId, NEW_PASSWORD);
    
    console.log('🎉 Password reset completed successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  resetUserPassword();
}

module.exports = {
  resetUserPassword,
  getUserIdByEmail,
  updateUserPassword
};
